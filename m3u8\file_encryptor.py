#!/usr/bin/env python3
"""
通用文件加密处理器
支持除mp4视频和非动图图片外的所有文件格式加密
使用AES128-GCM算法进行高性能加密
"""

import os
import sys
import time
import secrets
import shutil
import hashlib
from pathlib import Path
from datetime import datetime
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
import logging

# 添加aes目录到Python路径以导入数据库队列
current_file = Path(__file__).resolve()
aes_path = str(current_file.parent.parent / 'aes')
if aes_path not in sys.path:
    sys.path.insert(0, aes_path)

try:
    from database_queue import get_database_queue
    DATABASE_QUEUE_AVAILABLE = True
    print("[OK] 数据库队列模块导入成功")
except ImportError as e:
    print(f"警告: 无法导入数据库队列模块，文件信息将不会保存到数据库: {e}")
    DATABASE_QUEUE_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('file_encryptor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FileEncryptor:
    """通用文件加密处理器"""

    def __init__(self, scan_dir="scanfile", output_dir="other"):
        self.scan_dir = Path(scan_dir)
        self.output_dir = Path(output_dir)
        self.monitor_interval = 2  # 监控间隔2秒

        # 初始化数据库队列
        self.db_queue = None
        if DATABASE_QUEUE_AVAILABLE:
            try:
                self.db_queue = get_database_queue()
                if not self.db_queue.running:
                    self.db_queue.start()
                logger.info("数据库队列初始化成功")
            except Exception as e:
                logger.warning(f"数据库队列初始化失败: {e}")
                self.db_queue = None

        # 确保目录存在
        self.scan_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)

        # 排除的文件格式（mp4视频和非动图图片）
        self.excluded_extensions = {
            # MP4视频
            '.mp4',
            # 非动图图片格式
            '.jpg', '.jpeg', '.png', '.webp', '.avif', '.bmp', '.tiff', '.tif',
            '.ico', '.svg', '.heic', '.heif'
        }

        logger.info(f"文件加密器初始化完成")
        logger.info(f"扫描目录: {self.scan_dir.absolute()}")
        logger.info(f"输出目录: {self.output_dir.absolute()}")
        logger.info(f"排除格式: {', '.join(self.excluded_extensions)}")
    
    def generate_secure_key_iv(self):
        """生成安全的16字节密钥和随机IV"""
        key = secrets.token_bytes(16)  # AES128需要16字节密钥
        iv = secrets.token_bytes(12)   # GCM模式推荐12字节IV
        return key, iv

    def calculate_sha1(self, file_path: Path) -> str:
        """计算文件的SHA1哈希值"""
        try:
            sha1_hash = hashlib.sha1()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha1_hash.update(chunk)
            return sha1_hash.hexdigest()
        except Exception as e:
            logger.error(f"计算SHA1失败 {file_path}: {e}")
            return ""

    def _save_file_to_database_direct(self, sha1_hash, original_filename, file_path, password, iv,
                                    file_created_date=None, file_size_mb=None, tag="", txt=""):
        """直接保存文件记录到数据库"""
        import sqlite3
        from pathlib import Path

        db_file = Path(__file__).resolve().parent.parent / 'aes' / 'image_encryption.db'

        try:
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()

            # 检查是否已存在
            cursor.execute('SELECT id FROM encrypted_files WHERE sha1_hash = ?', (sha1_hash,))
            existing = cursor.fetchone()

            if existing:
                # 更新
                cursor.execute('''
                    UPDATE encrypted_files
                    SET original_filename = ?, file_path = ?, password = ?, iv = ?,
                        file_created_date = ?, file_size_mb = ?, tag = ?, txt = ?,
                        encryption_date = CURRENT_TIMESTAMP
                    WHERE sha1_hash = ?
                ''', (original_filename, file_path, password, iv, file_created_date,
                      file_size_mb, tag, txt, sha1_hash))
                logger.info(f"更新文件数据库记录: {original_filename}")
            else:
                # 插入
                cursor.execute('''
                    INSERT INTO encrypted_files
                    (sha1_hash, original_filename, file_path, password, iv, file_created_date,
                     file_size_mb, tag, txt)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (sha1_hash, original_filename, file_path, password, iv, file_created_date,
                      file_size_mb, tag, txt))
                logger.info(f"新增文件数据库记录: {original_filename}")

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"直接保存文件记录失败: {e}")
            raise

    def should_encrypt_file(self, file_path):
        """判断文件是否应该被加密"""
        if not file_path.is_file():
            return False
        
        # 检查文件扩展名
        ext = file_path.suffix.lower()
        if ext in self.excluded_extensions:
            logger.debug(f"跳过排除格式文件: {file_path.name}")
            return False
        
        # 检查文件大小（跳过空文件）
        if file_path.stat().st_size == 0:
            logger.debug(f"跳过空文件: {file_path.name}")
            return False
        
        return True
    
    def encrypt_file(self, file_path, key=None, iv=None):
        """使用AES128-GCM加密文件"""
        try:
            # 如果没有提供密钥和IV，则生成新的
            if key is None or iv is None:
                key, iv = self.generate_secure_key_iv()

            # 创建AESGCM实例
            aesgcm = AESGCM(key)

            # 读取原文件内容
            with open(file_path, 'rb') as f:
                plaintext = f.read()

            # 加密数据
            ciphertext = aesgcm.encrypt(iv, plaintext, None)

            # 创建加密文件内容（IV + 密文）
            encrypted_data = iv + ciphertext

            # 覆盖原文件
            with open(file_path, 'wb') as f:
                f.write(encrypted_data)

            logger.info(f"文件加密成功: {file_path.name}")
            return True

        except Exception as e:
            logger.error(f"文件加密失败 {file_path.name}: {e}")
            return False
    
    def get_today_folder(self):
        """获取今天日期的文件夹路径"""
        today = datetime.now().strftime("%Y-%m-%d")
        today_folder = self.output_dir / today
        today_folder.mkdir(exist_ok=True)
        return today_folder
    
    def move_encrypted_file(self, file_path):
        """将加密后的文件移动到today文件夹"""
        try:
            today_folder = self.get_today_folder()
            destination = today_folder / file_path.name
            
            # 如果目标文件已存在，添加时间戳后缀
            if destination.exists():
                timestamp = datetime.now().strftime("%H%M%S")
                name_parts = file_path.name.rsplit('.', 1)
                if len(name_parts) == 2:
                    new_name = f"{name_parts[0]}_{timestamp}.{name_parts[1]}"
                else:
                    new_name = f"{file_path.name}_{timestamp}"
                destination = today_folder / new_name
            
            # 移动文件
            shutil.move(str(file_path), str(destination))
            logger.info(f"文件移动成功: {file_path.name} -> {destination}")
            return True
            
        except Exception as e:
            logger.error(f"文件移动失败 {file_path.name}: {e}")
            return False
    
    def process_file(self, file_path):
        """处理单个文件：加密并移动"""
        logger.info(f"开始处理文件: {file_path.name}")

        # 获取原始文件信息
        original_filename = file_path.name
        file_size = file_path.stat().st_size
        file_size_mb = round(file_size / (1024 * 1024), 2)

        # 获取文件创建日期
        file_created_date = datetime.fromtimestamp(file_path.stat().st_ctime).strftime("%Y-%m-%d %H:%M:%S")

        # 计算SHA1哈希
        sha1_hash = self.calculate_sha1(file_path)
        if not sha1_hash:
            logger.error(f"无法计算文件SHA1: {file_path.name}")
            return False

        # 生成密钥和IV
        key, iv = self.generate_secure_key_iv()
        password = key.hex()
        iv_hex = iv.hex()

        # 加密文件
        if self.encrypt_file(file_path, key, iv):
            # 移动到目标目录
            moved_file_path = None
            if self.move_encrypted_file(file_path):
                # 获取移动后的文件路径
                today_folder = self.get_today_folder()
                moved_file_path = today_folder / file_path.name
                if not moved_file_path.exists():
                    # 可能有时间戳后缀，查找实际文件
                    for f in today_folder.iterdir():
                        if f.name.startswith(file_path.stem):
                            moved_file_path = f
                            break

                # 保存到数据库队列
                if self.db_queue and moved_file_path:
                    try:
                        # 生成相对路径
                        relative_path = str(moved_file_path).replace('\\', '/')

                        # 直接写入数据库，绕过队列机制
                        self._save_file_to_database_direct(
                            sha1_hash=sha1_hash,
                            original_filename=original_filename,
                            file_path=relative_path,
                            password=password,
                            iv=iv_hex,
                            file_created_date=file_created_date,
                            file_size_mb=file_size_mb,
                            tag="",
                            txt=""
                        )
                        logger.info(f"文件信息已保存到数据库: {original_filename}")
                    except Exception as e:
                        logger.error(f"保存文件信息到数据库失败: {e}")

                logger.info(f"文件处理完成: {file_path.name}")
                return True

        logger.error(f"文件处理失败: {file_path.name}")
        return False
    
    def scan_and_process(self):
        """扫描并处理文件"""
        try:
            # 获取扫描目录中的所有文件
            files = [f for f in self.scan_dir.iterdir() if f.is_file()]
            
            if not files:
                return
            
            logger.info(f"发现 {len(files)} 个文件")
            
            # 处理每个文件
            for file_path in files:
                if self.should_encrypt_file(file_path):
                    self.process_file(file_path)
                else:
                    logger.debug(f"跳过文件: {file_path.name}")
                    
        except Exception as e:
            logger.error(f"扫描处理异常: {e}")
    
    def start_monitoring(self):
        """开始监控扫描"""
        logger.info("开始监控文件加密服务...")
        logger.info(f"监控间隔: {self.monitor_interval}秒")
        logger.info("按 Ctrl+C 停止服务")
        
        try:
            while True:
                self.scan_and_process()
                time.sleep(self.monitor_interval)
                
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在退出...")
        except Exception as e:
            logger.error(f"监控服务异常: {e}")
        finally:
            logger.info("文件加密服务已停止")

def main():
    """主函数"""
    # 创建加密器实例
    encryptor = FileEncryptor()
    
    # 开始监控
    encryptor.start_monitoring()

if __name__ == "__main__":
    main()
