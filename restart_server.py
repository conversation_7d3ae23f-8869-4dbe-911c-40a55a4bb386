#!/usr/bin/env python3
"""
简单的服务器重启脚本 - 支持非阻塞模式
"""

import http.server
import socketserver
import os
import sys
import json
import threading
import time
import signal
from urllib.parse import urlparse, unquote
from pathlib import Path

class HLSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    # 设置超时时间
    timeout = 30

    def setup(self):
        """设置连接"""
        super().setup()
        # 设置socket超时
        self.connection.settimeout(self.timeout)

    def end_headers(self):
        # 设置CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.send_header('Cache-Control', 'no-cache')
        self.send_header('Connection', 'close')  # 强制关闭连接，避免keep-alive
        super().end_headers()

    def log_message(self, format, *args):
        """自定义日志格式，包含客户端IP"""
        try:
            client_ip = self.client_address[0]
            print(f"[{self.log_date_time_string()}] {client_ip} - {format % args}")
        except:
            # 如果获取客户端IP失败，使用默认日志
            print(f"[{self.log_date_time_string()}] - {format % args}")

    def handle_one_request(self):
        """重写请求处理，添加异常处理和超时控制"""
        try:
            super().handle_one_request()
        except ConnectionAbortedError:
            # 客户端中断连接，这是正常情况
            pass
        except ConnectionResetError:
            # 连接被重置，这也是正常情况
            pass
        except OSError as e:
            # 网络相关错误
            if e.errno not in [10053, 10054, 10058]:  # 常见的网络断开错误码
                print(f"[{self.log_date_time_string()}] 网络错误: {e}")
        except Exception as e:
            print(f"[{self.log_date_time_string()}] 处理请求时出错: {e}")
            try:
                self.send_error(500, f"Internal server error: {str(e)}")
            except:
                pass  # 如果连接已断开，忽略发送错误的异常
        finally:
            # 确保连接被关闭
            try:
                self.connection.close()
            except:
                pass

    def guess_type(self, path):
        """为HLS文件设置正确的MIME类型"""
        if path.endswith('.m3u8'):
            return 'application/vnd.apple.mpegurl'
        elif path.endswith('.ts'):
            return 'video/mp2t'
        elif path.endswith('.key'):
            return 'application/octet-stream'
        elif path.endswith('.iv'):
            return 'application/octet-stream'
        
        return super().guess_type(path)

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        # 处理M3U8文件，自动修复BYTERANGE问题
        if self.path.endswith('/playlist.m3u8'):
            self.handle_fixed_m3u8()
            return
        
        # 默认处理
        super().do_GET()

    def handle_fixed_m3u8(self):
        """处理M3U8文件，自动修复BYTERANGE问题"""
        try:
            # 获取实际的文件路径
            file_path = self.path.lstrip('/')
            if not os.path.exists(file_path):
                self.send_error(404, "File not found")
                return
            
            # 读取原始M3U8文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修复内容
            fixed_content = self.fix_m3u8_content(content)
            
            # 发送修复后的内容
            self.send_response(200)
            self.send_header('Content-Type', 'application/vnd.apple.mpegurl')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
            self.wfile.write(fixed_content.encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"Error processing M3U8: {str(e)}")

    def fix_m3u8_content(self, content):
        """修复M3U8内容，移除BYTERANGE"""
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # 跳过BYTERANGE行
            if line.startswith('#EXT-X-BYTERANGE:'):
                continue
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)

class NonBlockingHTTPServer:
    """非阻塞HTTP服务器类"""

    def __init__(self, port=8080):
        self.port = port
        self.httpd = None
        self.server_thread = None
        self.running = False

    def start(self):
        """启动服务器"""
        if self.running:
            print("服务器已经在运行中")
            return False

        try:
            print(f"正在启动服务器，端口: {self.port}")

            # 确保在正确的目录中
            os.chdir(os.path.dirname(os.path.abspath(__file__)))
            print(f"工作目录: {os.getcwd()}")

            # 检查m3u8目录是否存在
            if not os.path.exists('m3u8'):
                print("警告: m3u8目录不存在")
            elif not os.path.exists('m3u8/video'):
                print("警告: m3u8/video目录不存在")
            else:
                video_count = len([d for d in os.listdir('m3u8/video') if os.path.isdir(os.path.join('m3u8/video', d))])
                print(f"发现 {video_count} 个视频目录")

            # 创建多线程服务器，每个请求在独立线程中处理
            print("创建多线程HTTP服务器...")
            self.httpd = socketserver.ThreadingTCPServer(("0.0.0.0", self.port), HLSHTTPRequestHandler)
            self.httpd.allow_reuse_address = True
            self.httpd.daemon_threads = True  # 守护线程，主线程退出时自动清理

            # 在单独的线程中运行服务器
            print("启动服务器线程...")
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()

            # 等待一小段时间确保服务器启动
            time.sleep(0.5)

            self.running = True

            print(f"✓ HLS视频服务器启动成功，端口: {self.port}")
            print(f"✓ 本地访问: http://localhost:{self.port}/fixed_player.html")
            print(f"✓ 局域网访问: http://[您的IP地址]:{self.port}/fixed_player.html")
            print("✓ 服务器运行在多线程模式，不会阻塞主进程")
            print("=" * 50)

            return True

        except OSError as e:
            if e.errno == 10048:  # 端口被占用
                print(f"错误: 端口 {self.port} 已被占用，请尝试其他端口或结束占用该端口的进程")
            else:
                print(f"错误: 无法绑定到端口 {self.port}: {e}")
            return False
        except Exception as e:
            print(f"启动服务器时出错: {e}")
            return False

    def _run_server(self):
        """在线程中运行服务器"""
        try:
            self.httpd.serve_forever()
        except Exception as e:
            print(f"服务器运行时出错: {e}")
        finally:
            self.running = False

    def stop(self):
        """停止服务器"""
        if not self.running:
            print("服务器未运行")
            return

        print("正在停止服务器...")
        if self.httpd:
            self.httpd.shutdown()
            self.httpd.server_close()

        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=5)

        self.running = False
        print("服务器已停止")

    def is_running(self):
        """检查服务器是否在运行"""
        return self.running

    def wait_for_shutdown(self):
        """等待服务器关闭（阻塞模式）"""
        if self.server_thread and self.server_thread.is_alive():
            try:
                self.server_thread.join()
            except KeyboardInterrupt:
                print("\n收到中断信号，正在停止服务器...")
                self.stop()

def main():
    port = 8080
    daemon_mode = False

    # 解析命令行参数
    for arg in sys.argv[1:]:
        if arg == '--daemon' or arg == '-d':
            daemon_mode = True
        else:
            try:
                port = int(arg)
            except ValueError:
                print(f"无效的端口号: {arg}")
                print("用法: python restart_server.py [端口号] [--daemon|-d]")
                sys.exit(1)

    # 创建服务器实例
    server = NonBlockingHTTPServer(port)

    # 设置信号处理器
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在停止服务器...")
        server.stop()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 启动服务器
    if server.start():
        if daemon_mode:
            print("服务器运行在守护模式，主进程将退出")
            print("使用 'pkill -f restart_server.py' 停止服务器")
        else:
            print("按 Ctrl+C 停止服务器")
            try:
                server.wait_for_shutdown()
            except KeyboardInterrupt:
                server.stop()
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
