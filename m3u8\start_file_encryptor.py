#!/usr/bin/env python3
"""
文件加密器启动脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from file_encryptor import FileEncryptor
    
    def main():
        print("=" * 50)
        print("通用文件加密器")
        print("=" * 50)
        print("功能：监控scanfile目录，加密符合条件的文件")
        print("排除：mp4视频和非动图图片格式")
        print("算法：AES128-GCM高性能加密")
        print("输出：加密后移动到other/今日日期文件夹")
        print("=" * 50)
        
        # 检查必要目录
        scan_dir = current_dir / "scanfile"
        other_dir = current_dir / "other"
        
        if not scan_dir.exists():
            print(f"创建扫描目录: {scan_dir}")
            scan_dir.mkdir(exist_ok=True)
        
        if not other_dir.exists():
            print(f"创建输出目录: {other_dir}")
            other_dir.mkdir(exist_ok=True)
        
        # 启动加密器
        encryptor = FileEncryptor()
        encryptor.start_monitoring()
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所需依赖: pip install cryptography")
    sys.exit(1)
except Exception as e:
    print(f"启动失败: {e}")
    sys.exit(1)
