#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一启动脚本 - 一键启动所有加密服务
包括：图片加密服务、视频加密服务、其他文件加密服务、数据库队列服务
支持Windows和Linux平台
"""

import os
import sys
import time
import signal
import threading
import subprocess
from pathlib import Path
from typing import List, Dict, Any

# 添加必要的路径
current_dir = Path(__file__).parent
aes_dir = current_dir / 'aes'
m3u8_dir = current_dir / 'm3u8'

sys.path.append(str(aes_dir))
sys.path.append(str(m3u8_dir))

class UnifiedLauncher:
    """统一启动器"""
    
    def __init__(self):
        self.services = {}
        self.running = False
        self.threads = []
        
        # 检查依赖
        self.check_dependencies()
        
        print("=" * 60)
        print("统一加密服务启动器")
        print("=" * 60)
        print("功能：")
        print("  - 图片加密服务 (aes/main.py)")
        print("  - 视频加密服务 (m3u8/video_processor.py)")
        print("  - 其他文件加密服务 (m3u8/file_encryptor.py)")
        print("  - 数据库队列服务 (自动启动)")
        print("=" * 60)
    
    def check_dependencies(self):
        """检查依赖项"""
        print("检查依赖项...")
        
        # 检查必要的目录
        required_dirs = [aes_dir, m3u8_dir]
        for dir_path in required_dirs:
            if not dir_path.exists():
                print(f"✗ 缺少必要目录: {dir_path}")
                sys.exit(1)
        
        # 检查必要的文件
        required_files = [
            aes_dir / 'main.py',
            aes_dir / 'database_queue.py',
            m3u8_dir / 'video_processor.py',
            m3u8_dir / 'file_encryptor.py',
            m3u8_dir / 'start_file_encryptor.py'
        ]

        missing_files = []
        for file_path in required_files:
            if not file_path.exists():
                missing_files.append(str(file_path))

        if missing_files:
            print(f"✗ 缺少必要文件:")
            for file_path in missing_files:
                print(f"  - {file_path}")
            sys.exit(1)
        
        print("✓ 依赖检查通过")
    
    def start_image_service(self):
        """启动图片加密服务"""
        try:
            print("启动图片加密服务...")

            # 使用subprocess启动图片处理服务
            cmd = [sys.executable, '-u', 'main.py']  # -u 参数强制无缓冲输出
            process = subprocess.Popen(
                cmd,
                cwd=str(aes_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=0,  # 无缓冲
                universal_newlines=True,
                env={**os.environ, 'PYTHONUNBUFFERED': '1'}  # 设置环境变量
            )

            self.services['image'] = process

            # 读取输出
            while self.running and process.poll() is None:
                line = process.stdout.readline()
                if line:
                    print(f"[图片服务] {line.strip()}")

        except Exception as e:
            print(f"图片加密服务异常: {e}")

    def start_video_service(self):
        """启动视频加密服务"""
        try:
            print("启动视频加密服务...")

            # 使用subprocess启动视频处理服务
            cmd = [sys.executable, 'video_processor.py']
            process = subprocess.Popen(
                cmd,
                cwd=str(m3u8_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.services['video'] = process

            # 读取输出
            while self.running and process.poll() is None:
                line = process.stdout.readline()
                if line:
                    print(f"[视频服务] {line.strip()}")

        except Exception as e:
            print(f"视频加密服务异常: {e}")

    def start_file_service(self):
        """启动其他文件加密服务"""
        try:
            print("启动其他文件加密服务...")

            # 使用subprocess启动文件加密服务
            cmd = [sys.executable, 'start_file_encryptor.py']
            process = subprocess.Popen(
                cmd,
                cwd=str(m3u8_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.services['file'] = process

            # 读取输出
            while self.running and process.poll() is None:
                line = process.stdout.readline()
                if line:
                    print(f"[文件服务] {line.strip()}")

        except Exception as e:
            print(f"其他文件加密服务异常: {e}")
    
    def start_database_service(self):
        """启动数据库队列服务（由各服务自动启动，这里只是监控）"""
        try:
            print("数据库队列服务监控中...")

            # 导入数据库队列模块
            try:
                import sys
                sys.path.append('aes')
                from database_queue import get_database_queue
                print("[OK] 数据库队列模块导入成功")
            except ImportError as e:
                print(f"[WARN] 无法导入数据库队列模块: {e}")
                # 简单监控模式
                while self.running:
                    time.sleep(30)
                return

            last_stats_time = time.time()
            stats_interval = 60  # 每60秒显示一次统计信息

            while self.running:
                try:
                    # 获取数据库队列实例
                    db_queue = get_database_queue()

                    # 定期显示统计信息
                    current_time = time.time()
                    if current_time - last_stats_time >= stats_interval:
                        if db_queue and db_queue.running:
                            stats = db_queue.get_queue_stats()
                            if stats['total_operations'] > 0:  # 只在有操作时显示
                                print(f"\n[数据库队列统计] 队列:{stats['queue_size']} | "
                                      f"总计:{stats['total_operations']} | "
                                      f"成功:{stats['successful_operations']} | "
                                      f"失败:{stats['failed_operations']} | "
                                      f"锁重试:{stats['database_lock_retries']}")
                        last_stats_time = current_time

                    time.sleep(5)  # 每5秒检查一次

                except Exception as monitor_error:
                    print(f"[WARN] 数据库队列监控错误: {monitor_error}")
                    time.sleep(10)  # 出错时等待更长时间

        except KeyboardInterrupt:
            print("数据库队列服务监控已停止")
        except Exception as e:
            print(f"数据库队列服务监控异常: {e}")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止所有服务...")
        self.stop_all_services()
    
    def stop_all_services(self):
        """停止所有服务"""
        self.running = False

        print("正在停止所有服务...")

        # 停止所有子进程
        for service_name, process in self.services.items():
            try:
                if process and process.poll() is None:
                    print(f"正在停止 {service_name} 服务...")
                    process.terminate()

                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        print(f"✓ {service_name} 服务已停止")
                    except subprocess.TimeoutExpired:
                        print(f"⚠ {service_name} 服务未响应，强制终止...")
                        process.kill()
                        process.wait()
                        print(f"✓ {service_name} 服务已强制终止")
            except Exception as e:
                print(f"⚠ 停止 {service_name} 服务失败: {e}")

        # 停止数据库队列
        try:
            sys.path.append(str(aes_dir))
            from database_queue import stop_database_queue
            stop_database_queue()
            print("✓ 数据库队列已停止")
        except Exception as e:
            print(f"⚠ 停止数据库队列失败: {e}")

        # 等待所有线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=3)

        print("所有服务已停止")
        sys.exit(0)
    
    def start_all_services(self):
        """启动所有服务"""
        self.running = True
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("正在启动所有服务...")
        
        # 创建服务线程
        services = [
            ("图片加密服务", self.start_image_service),
            ("视频加密服务", self.start_video_service),
            ("其他文件加密服务", self.start_file_service),
            ("数据库队列监控", self.start_database_service)
        ]
        
        for service_name, service_func in services:
            thread = threading.Thread(target=service_func, name=service_name, daemon=True)
            thread.start()
            self.threads.append(thread)
            print(f"✓ {service_name} 已启动")
            time.sleep(1)  # 错开启动时间
        
        print("\n" + "=" * 60)
        print("所有服务已启动完成！")
        print("=" * 60)
        print("监控目录：")
        print(f"  - 图片文件: {aes_dir}/temp/")
        print(f"  - 视频文件: {m3u8_dir}/temp/")
        print(f"  - 其他文件: {m3u8_dir}/scanfile/")
        print("=" * 60)
        print("按 Ctrl+C 停止所有服务")
        print("=" * 60)
        
        # 主线程保持运行
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop_all_services()

def main():
    """主函数"""
    try:
        launcher = UnifiedLauncher()
        launcher.start_all_services()
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
