#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片处理和加密系统
每2秒检测temp目录中的图片文件，生成缩略图，转换为AVIF格式并加密
"""

import os
import time
import sqlite3
import hashlib
import secrets
import threading
import subprocess
import random
from pathlib import Path
from typing import List, Tuple, Optional
from datetime import datetime
from database_queue import start_database_queue, stop_database_queue, get_database_queue

# 尝试导入依赖包，如果失败则给出提示
try:
    from cryptography.hazmat.primitives.ciphers.aead import AESGCM
    CRYPTO_AVAILABLE = True
except ImportError:
    print("警告: cryptography 包未安装，请运行: pip install cryptography")
    CRYPTO_AVAILABLE = False

try:
    from PIL import Image
    from PIL.ExifTags import TAGS, GPSTAGS
    PIL_AVAILABLE = True
except ImportError:
    print("警告: Pillow 包未安装，请运行: pip install Pillow")
    PIL_AVAILABLE = False

# 检查本地AVIF工具（跨平台支持）
def detect_avif_tools():
    """检测AVIF工具，支持Windows和Linux"""
    import platform

    system = platform.system().lower()

    if system == 'windows':
        encoder_name = 'avifenc.exe'
        decoder_name = 'avifdec.exe'
    else:  # Linux, macOS, etc.
        encoder_name = 'avifenc'
        decoder_name = 'avifdec'

    encoder_path = Path(encoder_name)
    decoder_path = Path(decoder_name)

    # 检查当前目录
    if encoder_path.exists() and decoder_path.exists():
        return encoder_path, decoder_path, True

    # 检查系统PATH中是否有这些工具
    import shutil
    encoder_system = shutil.which(encoder_name)
    decoder_system = shutil.which(decoder_name)

    if encoder_system and decoder_system:
        return Path(encoder_system), Path(decoder_system), True

    return None, None, False

AVIF_ENCODER, AVIF_DECODER, AVIF_AVAILABLE = detect_avif_tools()

if AVIF_AVAILABLE:
    print(f"[OK] 检测到AVIF工具: {AVIF_ENCODER}")
else:
    print("警告: 未找到AVIF工具，将使用WebP格式代替")

# 检查核心依赖是否可用
if not all([CRYPTO_AVAILABLE, PIL_AVAILABLE]):
    print("\n缺少必要的依赖包，请先安装:")
    print("pip install cryptography Pillow")
    print("\n或者运行: pip install -r requirements.txt")
    if not CRYPTO_AVAILABLE:
        print("pip install cryptography")
    if not PIL_AVAILABLE:
        print("pip install Pillow")
    print("\n安装完成后重新运行程序。")
    exit(1)

if not AVIF_AVAILABLE:
    print("注意: 由于AVIF支持不可用，将使用WebP格式进行转换")

# 支持的图片格式
SUPPORTED_FORMATS = {'.jpg', '.jpeg', '.png', '.webp', '.avif', '.heic'}

# 获取脚本所在目录，确保路径正确
SCRIPT_DIR = Path(__file__).parent
TEMP_DIR = SCRIPT_DIR / 'temp'
FILE_DIR = SCRIPT_DIR / 'file'  # 处理完成后的文件存储目录
DB_FILE = SCRIPT_DIR / 'image_encryption.db'

class ImageProcessor:
    def __init__(self):
        # 启动数据库队列
        self.db_queue = start_database_queue()
        self.ensure_temp_dir()
        self.ensure_file_dir()

    def wait_random_delay(self):
        """等待随机延迟（100-1000毫秒）"""
        delay = random.randint(100, 1000) / 1000.0  # 转换为秒
        time.sleep(delay)

    def execute_with_retry(self, operation_name: str, operation_func, max_retries: int = 2):
        """执行数据库操作，支持重试机制"""
        for attempt in range(max_retries + 1):
            try:
                return operation_func()
            except sqlite3.OperationalError as e:
                error_msg = str(e).lower()
                if "database is locked" in error_msg or "database disk image is malformed" in error_msg:
                    if attempt < max_retries:
                        print(f"[WARN] {operation_name}失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                        print(f"等待 {100 + attempt * 200}-{1000 + attempt * 200}ms 后重试...")
                        self.wait_random_delay()
                        continue
                    else:
                        print(f"[ERROR] {operation_name}最终失败: {e}")
                        raise
                else:
                    print(f"[ERROR] {operation_name}失败: {e}")
                    raise
            except Exception as e:
                if attempt < max_retries and ("正在使用此文件" in str(e) or "being used by another process" in str(e)):
                    print(f"[WARN] {operation_name}失败 (尝试 {attempt + 1}/{max_retries + 1}): 文件被占用")
                    print(f"等待 {100 + attempt * 200}-{1000 + attempt * 200}ms 后重试...")
                    self.wait_random_delay()
                    continue
                else:
                    print(f"[ERROR] {operation_name}失败: {e}")
                    raise
        

        
    def ensure_temp_dir(self):
        """确保temp目录存在"""
        TEMP_DIR.mkdir(exist_ok=True)

    def ensure_file_dir(self):
        """确保file目录存在"""
        FILE_DIR.mkdir(exist_ok=True)
        
    def get_image_files(self) -> List[Path]:
        """获取temp目录中的所有图片文件（排除已处理的文件）"""
        image_files = []
        if not TEMP_DIR.exists():
            return image_files

        for file_path in TEMP_DIR.iterdir():
            if not file_path.is_file():
                continue

            # 检查文件扩展名
            if file_path.suffix.lower() not in SUPPORTED_FORMATS:
                continue

            # 跳过缩略图文件
            if '_tagimg' in file_path.stem:
                continue

            # 跳过可能是处理失败留下的临时AVIF文件
            # 临时AVIF文件的特征：由程序转换生成，通常在处理其他格式文件时产生
            # 这里我们简化逻辑：只有当AVIF文件的修改时间很新（几分钟内）且存在同名原文件时才认为是临时文件
            if file_path.suffix.lower() == '.avif':
                import time
                current_time = time.time()
                file_mtime = file_path.stat().st_mtime

                # 如果文件是最近5分钟内创建的，且存在同名的其他格式文件，则可能是临时文件
                if (current_time - file_mtime) < 300:  # 5分钟 = 300秒
                    stem_name = file_path.stem
                    has_source_file = False
                    for other_file in TEMP_DIR.iterdir():
                        if (other_file != file_path and
                            other_file.stem == stem_name and
                            other_file.suffix.lower() in {'.jpg', '.jpeg', '.png', '.webp', '.heic'}):
                            has_source_file = True
                            break

                    if has_source_file:
                        print(f"跳过临时AVIF文件: {file_path}")
                        # 尝试删除这个临时文件
                        try:
                            file_path.unlink()
                            print(f"清理临时AVIF文件: {file_path}")
                        except:
                            pass
                        continue
                # 其他情况下，正常处理AVIF文件

            # 验证是否为有效的图片文件
            if not self.is_valid_image(file_path):
                print(f"跳过无效图片文件: {file_path}")
                continue

            image_files.append(file_path)
        return image_files

    def is_valid_image(self, file_path: Path) -> bool:
        """验证是否为有效的图片文件"""
        try:
            # 尝试打开图片并获取基本信息
            with Image.open(file_path) as img:
                # 获取图片格式和尺寸，这会触发基本的验证
                format_info = img.format
                size_info = img.size
                # 检查是否有有效的尺寸
                if size_info[0] > 0 and size_info[1] > 0:
                    return True
                return False
        except Exception as e:
            print(f"图片验证失败 {file_path}: {e}")
            return False
        
    def generate_thumbnail(self, image_path: Path) -> Path:
        """生成JPG缩略图"""
        try:
            with Image.open(image_path) as img:
                # 转换为RGB模式（处理RGBA等格式）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 计算缩略图尺寸（保持宽高比，最大边不超过200px）
                img.thumbnail((200, 200), Image.Resampling.LANCZOS)
                
                # 生成缩略图文件名
                thumbnail_name = f"{image_path.stem}_tagimg.jpg"
                thumbnail_path = image_path.parent / thumbnail_name
                
                # 保存缩略图
                img.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
                return thumbnail_path
                
        except Exception as e:
            print(f"生成缩略图失败 {image_path}: {e}")
            return None
            
    def convert_to_avif(self, image_path: Path) -> Path:
        """转换图片为AVIF格式"""
        try:
            # 生成AVIF文件名
            avif_name = f"{image_path.stem}.avif"
            avif_path = image_path.parent / avif_name

            if AVIF_AVAILABLE:
                # 使用avifenc工具转换
                # avifenc的正确参数格式: avifenc [options] input.jpg output.avif
                # 优化参数以平衡质量、文件大小和速度
                cmd = [
                    str(AVIF_ENCODER),
                    str(image_path),      # 输入文件
                    str(avif_path),       # 输出文件
                    '--min', '0',        # 最小质量 (降低以提高速度)
                    '--max', '82',        # 最大质量 (80%质量对应约75)
                    '--speed', '6',       # 速度参数 (8较快，平衡质量和速度)
                ]

                # 使用字节模式避免编码问题
                try:
                    result = subprocess.run(cmd, capture_output=True, timeout=30)
                    if result.returncode == 0:
                        return avif_path
                    else:
                        # 尝试解码错误信息
                        try:
                            error_msg = result.stderr.decode('utf-8', errors='ignore')
                        except:
                            error_msg = "编码错误，无法显示详细信息"
                        print(f"avifenc转换失败: {error_msg}")
                        return self.convert_to_webp(image_path)
                except subprocess.TimeoutExpired:
                    print(f"avifenc转换超时: {image_path}")
                    return self.convert_to_webp(image_path)
            else:
                # 回退到WebP格式
                return self.convert_to_webp(image_path)

        except Exception as e:
            print(f"转换AVIF失败 {image_path}: {e}")
            # 清理可能生成的损坏AVIF文件
            if avif_path and avif_path.exists():
                try:
                    avif_path.unlink()
                    print(f"清理损坏的AVIF文件: {avif_path}")
                except:
                    pass
            return self.convert_to_webp(image_path)

    def convert_to_webp(self, image_path: Path) -> Path:
        """转换图片为WebP格式（AVIF的备选方案）"""
        try:
            with Image.open(image_path) as img:
                # 生成WebP文件名
                webp_name = f"{image_path.stem}.webp"
                webp_path = image_path.parent / webp_name

                # 保存为WebP格式，80%质量
                img.save(webp_path, 'WebP', quality=82, optimize=True)
                return webp_path

        except Exception as e:
            print(f"转换WebP失败 {image_path}: {e}")
            return None
            
    def generate_secure_password(self) -> str:
        """生成安全的16位随机密码"""
        return secrets.token_urlsafe(16)[:16]

    def generate_random_suffix(self) -> str:
        """生成6位随机字符串"""
        import string
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(6))

    def extract_exif_info(self, image_path: Path) -> dict:
        """提取图片的EXIF信息"""
        exif_info = {
            'capture_date': None,
            'file_size_mb': None,
            'image_width': None,
            'image_height': None,
            'gps_latitude': None,
            'gps_longitude': None
        }

        try:
            # 总是获取文件大小（MB）- 这个不依赖EXIF，精确到0.01MB
            file_size_bytes = image_path.stat().st_size
            exif_info['file_size_mb'] = round(file_size_bytes / (1024 * 1024), 2)

            # 打开图片并获取基本信息
            with Image.open(image_path) as img:
                # 总是获取图片尺寸 - 这个也不依赖EXIF
                exif_info['image_width'] = img.width
                exif_info['image_height'] = img.height

                # 尝试获取EXIF数据（可能为空）
                try:
                    exif_data = img._getexif()
                    if exif_data is not None:
                        # 提取拍摄日期
                        for tag_id, value in exif_data.items():
                            tag = TAGS.get(tag_id, tag_id)

                            # 拍摄日期
                            if tag in ['DateTime', 'DateTimeOriginal', 'DateTimeDigitized']:
                                if value and exif_info['capture_date'] is None:
                                    exif_info['capture_date'] = str(value)

                            # GPS信息
                            elif tag == 'GPSInfo':
                                gps_data = {}
                                for gps_tag_id, gps_value in value.items():
                                    gps_tag = GPSTAGS.get(gps_tag_id, gps_tag_id)
                                    gps_data[gps_tag] = gps_value

                                # 解析GPS坐标
                                lat, lon = self._parse_gps_coordinates(gps_data)
                                if lat is not None and lon is not None:
                                    exif_info['gps_latitude'] = lat
                                    exif_info['gps_longitude'] = lon
                except Exception:
                    # EXIF数据提取失败，但文件大小和尺寸已经获取到了
                    pass

        except Exception as e:
            print(f"[WARN] 提取文件信息失败 {image_path}: {e}")
            # 即使出错，也尝试获取文件大小，精确到0.01MB
            try:
                file_size_bytes = image_path.stat().st_size
                exif_info['file_size_mb'] = round(file_size_bytes / (1024 * 1024), 2)
            except:
                pass

        return exif_info

    def _parse_gps_coordinates(self, gps_data: dict) -> tuple:
        """解析GPS坐标"""
        try:
            # 获取纬度
            lat_ref = gps_data.get('GPSLatitudeRef')
            lat_data = gps_data.get('GPSLatitude')

            # 获取经度
            lon_ref = gps_data.get('GPSLongitudeRef')
            lon_data = gps_data.get('GPSLongitude')

            if not all([lat_ref, lat_data, lon_ref, lon_data]):
                return None, None

            # 转换纬度
            lat = self._convert_to_degrees(lat_data)
            if lat_ref == 'S':
                lat = -lat

            # 转换经度
            lon = self._convert_to_degrees(lon_data)
            if lon_ref == 'W':
                lon = -lon

            return round(lat, 6), round(lon, 6)

        except Exception as e:
            print(f"[WARN] 解析GPS坐标失败: {e}")
            return None, None

    def _convert_to_degrees(self, value):
        """将GPS坐标从度分秒格式转换为十进制度数"""
        try:
            d, m, s = value
            return float(d) + float(m) / 60.0 + float(s) / 3600.0
        except:
            return float(value[0]) if value else 0.0

    def check_filename_conflict_and_rename(self, image_path: Path) -> Path:
        """检查今日文件夹中是否存在同名文件，如果存在则重命名新文件"""
        try:
            # 获取今天的日期（年-月-日格式）
            today = datetime.now().strftime("%Y-%m-%d")

            # 创建日期子文件夹路径
            date_dir = FILE_DIR / today

            # 如果日期文件夹不存在，说明没有冲突
            if not date_dir.exists():
                return image_path

            # 检查是否存在同名文件
            target_file = date_dir / image_path.name
            if not target_file.exists():
                return image_path

            # 存在同名文件，需要重命名
            print(f"[WARN] 检测到文件名冲突: {image_path.name} 已存在于今日文件夹中")

            # 生成新的文件名：原文件名 + 随机6位字符串
            stem = image_path.stem
            suffix = image_path.suffix
            random_suffix = self.generate_random_suffix()
            new_name = f"{stem}_{random_suffix}{suffix}"
            new_path = image_path.parent / new_name

            # 重命名文件
            image_path.rename(new_path)
            print(f"[OK] 文件已重命名: {image_path.name} -> {new_name}")

            return new_path

        except Exception as e:
            print(f"[WARN] 检查文件名冲突失败: {e}")
            return image_path  # 返回原路径
        
    def encrypt_file(self, file_path: Path, password: str) -> Tuple[bytes, bytes]:
        """使用AES256-GCM加密文件"""
        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                data = f.read()
            
            # 生成密钥和IV
            key = hashlib.sha256(password.encode()).digest()
            aesgcm = AESGCM(key)
            iv = secrets.token_bytes(12)  # GCM模式推荐12字节IV
            
            # 加密数据
            encrypted_data = aesgcm.encrypt(iv, data, None)
            
            return encrypted_data, iv
            
        except Exception as e:
            print(f"加密文件失败 {file_path}: {e}")
            return None, None
            
    def calculate_sha1(self, file_path: Path) -> str:
        """计算文件的SHA1哈希值"""
        try:
            sha1_hash = hashlib.sha1()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha1_hash.update(chunk)
            return sha1_hash.hexdigest()
        except Exception as e:
            print(f"计算SHA1失败 {file_path}: {e}")
            return ""
            
    def save_to_database(self, file_path: Path, password: str, iv: bytes, sha1_hash: str, txt: str = "", exif_info: dict = None):
        """保存加密信息到数据库队列"""
        try:
            # 确保使用相对路径，处理Windows路径问题
            try:
                if file_path.is_absolute():
                    relative_path = str(file_path.relative_to(Path.cwd()))
                else:
                    relative_path = str(file_path)
            except ValueError:
                # 如果relative_to失败，直接使用文件路径
                relative_path = str(file_path)

            # 统一使用正斜杠
            relative_path = relative_path.replace('\\', '/')
            iv_hex = iv.hex()

            # 准备EXIF信息
            if exif_info is None:
                exif_info = {}

            # 通过队列保存到数据库
            self.db_queue.save_encrypted_image(
                file_path=relative_path,
                password=password,
                iv=iv_hex,
                sha1_hash=sha1_hash,
                txt=txt,
                capture_date=exif_info.get('capture_date'),
                file_size_mb=exif_info.get('file_size_mb'),
                image_width=exif_info.get('image_width'),
                image_height=exif_info.get('image_height'),
                gps_latitude=exif_info.get('gps_latitude'),
                gps_longitude=exif_info.get('gps_longitude')
            )

        except Exception as e:
            print(f"[ERROR] 添加到数据库队列失败: {e}")

    def move_to_file_dir(self, source_path: Path) -> Path:
        """将文件移动到file目录的日期子文件夹中"""
        try:
            # 获取今天的日期（年-月-日格式）
            today = datetime.now().strftime("%Y-%m-%d")

            # 创建日期子文件夹路径
            date_dir = FILE_DIR / today

            # 确保日期子文件夹存在
            date_dir.mkdir(parents=True, exist_ok=True)

            # 生成目标文件路径（在日期子文件夹中）
            target_path = date_dir / source_path.name

            # 如果目标文件已存在，生成唯一文件名
            counter = 1
            original_target = target_path
            while target_path.exists():
                stem = original_target.stem
                suffix = original_target.suffix
                target_path = date_dir / f"{stem}_{counter}{suffix}"
                counter += 1

            # 移动文件
            source_path.rename(target_path)
            print(f"文件已移动: {source_path} -> {target_path}")
            return target_path

        except Exception as e:
            print(f"移动文件失败 {source_path}: {e}")
            return source_path  # 返回原路径
            
    def process_image(self, image_path: Path):
        """处理单个图片文件"""
        print(f"处理图片: {image_path}")

        # 0. 首先检查文件名冲突并重命名（如果需要）
        image_path = self.check_filename_conflict_and_rename(image_path)

        # 初始化变量
        thumbnail_path = None
        converted_path = None
        temp_files_to_cleanup = []

        try:
            # 1. 提取原始图片的EXIF信息（在转换格式之前）
            print(f"[OK] 提取EXIF信息: {image_path}")
            exif_info = self.extract_exif_info(image_path)
            print(f"[OK] EXIF信息提取完成: 尺寸={exif_info.get('image_width')}x{exif_info.get('image_height')}, "
                  f"大小={exif_info.get('file_size_mb')}MB")
            if exif_info.get('capture_date'):
                print(f"  拍摄日期: {exif_info.get('capture_date')}")
            if exif_info.get('gps_latitude') and exif_info.get('gps_longitude'):
                print(f"  GPS坐标: {exif_info.get('gps_latitude')}, {exif_info.get('gps_longitude')}")

            # 2. 尝试生成缩略图（失败不影响主图处理）
            thumbnail_path = self.generate_thumbnail(image_path)
            if thumbnail_path:
                print(f"[OK] 缩略图生成成功: {thumbnail_path}")
            else:
                print(f"[WARN] 缩略图生成失败，继续处理主图: {image_path}")

            # 3. 尝试转换格式（AVIF文件跳过转换，其他格式转换失败则使用原格式）
            if image_path.suffix.lower() == '.avif':
                print(f"[OK] 检测到AVIF格式，跳过格式转换: {image_path}")
                # AVIF文件直接使用原文件进行加密
                file_to_encrypt = image_path
            else:
                converted_path = self.convert_to_avif(image_path)
                if converted_path and converted_path != image_path:
                    print(f"[OK] 格式转换成功: {image_path} -> {converted_path}")
                    temp_files_to_cleanup.append(converted_path)
                    # 使用转换后的文件进行加密
                    file_to_encrypt = converted_path
                else:
                    print(f"[WARN] 格式转换失败，使用原格式: {image_path}")
                    # 使用原文件进行加密
                    file_to_encrypt = image_path

            # 3. 生成密码
            password = self.generate_secure_password()

            # 4. 计算原文件SHA1（在加密前）
            original_sha1 = self.calculate_sha1(image_path)

            # 5. 加密主图文件
            encrypted_main, iv_main = self.encrypt_file(file_to_encrypt, password)
            if encrypted_main is None:
                print(f"[ERROR] 主图加密失败，跳过处理: {image_path}")
                self.cleanup_temp_files(thumbnail_path, *temp_files_to_cleanup)
                return

            # 6. 加密缩略图（如果存在）
            encrypted_thumb = None
            iv_thumb = None
            if thumbnail_path:
                encrypted_thumb, iv_thumb = self.encrypt_file(thumbnail_path, password)
                if encrypted_thumb is None:
                    print(f"[WARN] 缩略图加密失败，仅处理主图: {image_path}")
                    # 清理缩略图文件
                    if thumbnail_path.exists():
                        thumbnail_path.unlink()
                    thumbnail_path = None

            # 7. 覆盖原文件为加密版本
            with open(image_path, 'wb') as f:
                f.write(encrypted_main)
            print(f"[OK] 主图加密完成: {image_path}")

            # 8. 覆盖缩略图为加密版本（如果存在）
            if thumbnail_path and encrypted_thumb:
                with open(thumbnail_path, 'wb') as f:
                    f.write(encrypted_thumb)
                print(f"[OK] 缩略图加密完成: {thumbnail_path}")

            # 9. 清理临时文件
            for temp_file in temp_files_to_cleanup:
                if temp_file and temp_file.exists():
                    temp_file.unlink()
                    print(f"[OK] 清理临时文件: {temp_file}")

            # 10. 移动加密文件到file目录
            final_image_path = self.move_to_file_dir(image_path)
            final_thumbnail_path = None
            if thumbnail_path and thumbnail_path.exists():
                final_thumbnail_path = self.move_to_file_dir(thumbnail_path)

            # 11. 保存信息到数据库（使用最终路径和EXIF信息）
            # 只保存主图信息到数据库，不保存缩略图信息
            self.save_to_database(final_image_path, password, iv_main, original_sha1, exif_info=exif_info)

            success_msg = f"[OK] 成功处理并移动: {image_path} -> {final_image_path}"
            if final_thumbnail_path:
                success_msg += f" (含缩略图)"
            print(success_msg)

        except Exception as e:
            print(f"[ERROR] 处理图片失败 {image_path}: {e}")
            # 清理所有临时文件
            all_temp_files = [thumbnail_path] + temp_files_to_cleanup
            self.cleanup_temp_files(*all_temp_files)

    def cleanup_temp_files(self, *file_paths):
        """清理临时文件"""
        for file_path in file_paths:
            if file_path and isinstance(file_path, Path):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        print(f"清理临时文件: {file_path}")
                except Exception as e:
                    print(f"清理文件失败 {file_path}: {e}")
            
    def monitor_temp_directory(self):
        """监控temp目录"""
        print("开始监控temp目录...")
        
        while True:
            try:
                image_files = self.get_image_files()
                
                for image_file in image_files:
                    self.process_image(image_file)
                        
                time.sleep(2)  # 每2秒检查一次
                
            except KeyboardInterrupt:
                print("\n程序已停止")
                break
            except Exception as e:
                print(f"监控过程中出错: {e}")
                time.sleep(2)
                
    def is_already_processed(self, file_path: Path) -> bool:
        """检查文件是否已经处理过（通过文件名和SHA1检查）"""
        try:
            file_name = file_path.name

            # 尝试计算文件SHA1
            file_sha1 = None
            try:
                file_sha1 = self.calculate_sha1(file_path)
            except Exception:
                pass

            # 通过数据库队列检查
            return self.db_queue.is_file_processed(file_name, file_sha1)

        except Exception as e:
            print(f"[WARN] 检查文件处理状态失败: {e}")
            return False

def main():
    """主函数"""
    print("图片处理和加密系统启动")
    print("支持格式: JPG, PNG, WebP, AVIF, HEIC")
    print("监控目录: temp/")
    print("按 Ctrl+C 停止程序")

    processor = None
    try:
        processor = ImageProcessor()
        processor.monitor_temp_directory()
    except KeyboardInterrupt:
        print("\n程序已停止")
    finally:
        # 确保数据库队列正确停止
        print("正在停止数据库队列...")
        stop_database_queue()
        print("程序已完全退出")

if __name__ == "__main__":
    main()
